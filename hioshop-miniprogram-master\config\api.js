//const ApiRoot = 'https://ht.rxkjsdj.com';
const ApiRoot = 'http://127.0.0.1:8360';
const ApiRootUrl = ApiRoot + '/api/'

// 判断是否为IP环境（本地开发）
function isIPEnvironment(url) {
  // 匹配IP地址格式：http://192.168.x.x 或 http://127.0.0.1 等
  const ipPattern = /^https?:\/\/(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}|localhost|127\.0\.0\.1)/;
  return ipPattern.test(url);
}

// 根据环境选择支付接口
const payInterface = isIPEnvironment(ApiRoot) ? 'pay/preWeixinPaya' : 'pay/preWeixinPay';

module.exports = {
  ApiRoot: ApiRoot,
  // 登录
  AuthLoginByWeixin: ApiRootUrl + 'auth/loginByWeixin', //微信登录
  GetPhoneNumber: ApiRootUrl + 'auth/getPhoneNumber', //获取手机号
  ConfirmQrLogin: ApiRootUrl + 'auth/confirmQrLogin', //确认二维码登录
  // 首页
  IndexUrl: ApiRootUrl + 'index/appInfo', //首页数据接口
  // 分类
  CatalogList: ApiRootUrl + 'catalog/index', //分类目录全部分类数据接口
  CatalogCurrent: ApiRootUrl + 'catalog/current', //分类目录当前分类数据接口
  GetCurrentList: ApiRootUrl + 'catalog/currentlist',
  // 购物车
  CartAdd: ApiRootUrl + 'cart/add', // 添加商品到购物车
  CartList: ApiRootUrl + 'cart/index', //获取购物车的数据
  CartUpdate: ApiRootUrl + 'cart/update', // 更新购物车的商品//
  CartDelete: ApiRootUrl + 'cart/delete', // 删除购物车的商品
  CartChecked: ApiRootUrl + 'cart/checked', // 选择或取消选择商品
  CartGoodsCount: ApiRootUrl + 'cart/goodsCount', // 获取购物车商品件数
  CartCheckout: ApiRootUrl + 'cart/checkout', // 下单前信息确认
  // 商品
  GoodsCount: ApiRootUrl + 'goods/count', //统计商品总数
  GoodsDetail: ApiRootUrl + 'goods/detail', //获得商品的详情
  GoodsList: ApiRootUrl + 'goods/list', //获得商品列表
  GoodsShare: ApiRootUrl + 'goods/goodsShare', //获得商品的详情
  SaveUserId: ApiRootUrl + 'goods/saveUserId',
  // 收货地址
  AddressDetail: ApiRootUrl + 'address/addressDetail', //收货地址详情
  DeleteAddress: ApiRootUrl + 'address/deleteAddress', //保存收货地址
  SaveAddress: ApiRootUrl + 'address/saveAddress', //保存收货地址
  GetAddresses: ApiRootUrl + 'address/getAddresses',
  RegionList: ApiRootUrl + 'region/list', //获取区域列表
  PayPrepayId: ApiRootUrl + payInterface, //获取微信统一下单prepay_id（自动切换）
  OrderSubmit: ApiRootUrl + 'order/submit', // 提交订单
  OrderList: ApiRootUrl + 'order/list', //订单列表
  OrderDetail: ApiRootUrl + 'order/detail', //订单详情
  OrderDelete: ApiRootUrl + 'order/delete', //订单删除
  OrderCancel: ApiRootUrl + 'order/cancel', //取消订单
  OrderConfirm: ApiRootUrl + 'order/confirm', //物流详情
  RefundApply: ApiRootUrl + 'order/refundApply', //申请售后
  RefundDetail: ApiRootUrl + 'order/refundDetail', //售后详情
  RefundCancel: ApiRootUrl + 'order/refundCancel', //撤销售后
  SubmitReturnLogistics: ApiRootUrl + 'order/submitReturnLogistics', //提交退货物流信息
  OrderCount: ApiRootUrl + 'order/count', // 获取订单数
  OrderCountInfo: ApiRootUrl + 'order/orderCount', // 我的页面获取订单数状态
  OrderExpressInfo: ApiRootUrl + 'order/express', //物流信息
  OrderGoods: ApiRootUrl + 'order/orderGoods', // 获取checkout页面的商品列表
  // 足迹
  FootprintList: ApiRootUrl + 'footprint/list', //足迹列表
  FootprintDelete: ApiRootUrl + 'footprint/delete', //删除足迹
  // 搜索
  SearchIndex: ApiRootUrl + 'search/index', //搜索页面数据
  SearchHelper: ApiRootUrl + 'search/helper', //搜索帮助
  SearchClearHistory: ApiRootUrl + 'search/clearHistory', //搜索帮助
  ShowSettings: ApiRootUrl + 'settings/showSettings',
  SaveSettings: ApiRootUrl + 'settings/save',
  SettingsDetail: ApiRootUrl + 'settings/userDetail',
  UploadAvatar: ApiRootUrl + 'upload/uploadAvatar',
  GetBase64: ApiRootUrl + 'qrcode/getBase64', //获取商品详情二维码
  // 推广记录
  RecordPromotionVisit: ApiRootUrl + 'promotion/recordVisit', //记录推广访问
  // 佣金相关
  CommissionInfo: ApiRootUrl + 'commission/info', //获取佣金信息
  CommissionWithdraw: ApiRootUrl + 'commission/withdraw', //申请提现
  CommissionWithdraws: ApiRootUrl + 'commission/withdraws', //提现记录
  // 用户数据
  UserPoints: ApiRootUrl + 'user/points', //用户积分
  // 签到
  SignInData: ApiRootUrl + 'signin/data', //获取签到数据
  SignIn: ApiRootUrl + 'signin/checkin', //执行签到
  // 订单兑换
  OrderExchangeConfig: ApiRootUrl + 'order-exchange/config', //获取兑换配置
  OrderExchange: ApiRootUrl + 'order-exchange/exchange', //执行订单兑换
  OrderExchangeRecords: ApiRootUrl + 'order-exchange/records', //获取兑换记录

  // 优惠券
  CouponAvailable: ApiRootUrl + 'coupon/available', //获取可领取的优惠券
  CouponReceive: ApiRootUrl + 'coupon/receive', //领取优惠券
  CouponMy: ApiRootUrl + 'coupon/my', //我的优惠券
  CouponAvailableForOrder: ApiRootUrl + 'coupon/availableForOrder', //订单可用优惠券
  CouponAutoSelectBest: ApiRootUrl + 'coupon/autoSelectBest', //自动选择最优优惠券

  // 推广相关
  PromotionDataCenter: ApiRootUrl + 'promotion/dataCenter', //推广数据中心

  // 秒杀相关
  SeckillIndexTime: ApiRootUrl + 'seckill/index', //获取秒杀时间段配置
  SeckillListByTime: ApiRootUrl + 'seckill/list/{timeId}', //获取指定时间段的秒杀商品列表
  SeckillDetail: ApiRootUrl + 'seckill/detail/{id}', //获取秒杀商品详情
  HomeSeckill: ApiRootUrl + 'home/seckill', //获取首页秒杀数据
  SeckillRounds: ApiRootUrl + 'seckill/rounds', //获取实时秒杀轮次数据
  SeckillList: ApiRootUrl + 'seckill/list', //获取秒杀商品列表

};