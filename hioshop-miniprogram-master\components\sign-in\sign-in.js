Component({
  properties: {
    // 签到数据
    signData: {
      type: Object,
      value: {}
    }
  },

  data: {
    signList: [
      { is_sign: false, weekText: '周一' },
      { is_sign: false, weekText: '周二' },
      { is_sign: false, weekText: '周三' },
      { is_sign: false, weekText: '周四' },
      { is_sign: false, weekText: '周五' },
      { is_sign: false, weekText: '周六' },
      { is_sign: false, weekText: '周日' }
    ],
    weekFormatList: ['一', '二', '三', '四', '五', '六', '日']
  },

  lifetimes: {
    attached() {
      this.initSignData();
    }
  },

  methods: {
    // 初始化签到数据
    initSignData() {
      // 模拟签到数据，实际项目中应该从API获取
      const today = new Date().getDay(); // 0-6，0是周日
      const weekFormatList = this.data.weekFormatList;

      const signList = this.data.signList.map((item, index) => {
        // 使用CRMEB的weekFormat逻辑：周 + 星期文字
        item.weekText = '周' + weekFormatList[index];

        // 模拟前几天已签到
        if (index < today - 1) {
          item.is_sign = true;
        }
        return item;
      });

      this.setData({
        signList: signList
      });
    },

    // 签到按钮点击事件
    onSignTap() {
      // 触发父组件事件
      this.triggerEvent('sign', {
        message: '签到功能待开发'
      });
      
      // 临时提示
      wx.showToast({
        title: '签到功能待开发',
        icon: 'none',
        duration: 2000
      });
    }
  }
});
