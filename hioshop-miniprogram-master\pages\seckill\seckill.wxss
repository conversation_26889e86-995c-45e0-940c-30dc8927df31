/* pages/seckill/seckill.wxss */
.seckill-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  padding: 20rpx 30rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
}

/* 时间段选择器 */
.time-selector {
  background: #fff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.time-scroll {
  white-space: nowrap;
}

.time-list {
  display: inline-flex;
  padding: 0 30rpx;
}

.time-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 30rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  background: #f8f8f8;
  min-width: 120rpx;
  transition: all 0.3s ease;
}

.time-item.active {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: #fff;
}

.time-text {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.time-status {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.2);
}

.time-status.active {
  background: rgba(255, 255, 255, 0.3);
  color: #fff;
}

.time-status.ended {
  background: #ccc;
  color: #666;
}

.time-status.upcoming {
  background: #ffeaa7;
  color: #d63031;
}

/* 倒计时区域 */
.countdown-section {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.countdown-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.countdown-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
}

/* 商品容器 */
.products-container {
  flex: 1;
  padding: 0 30rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 商品网格 */
.products-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  padding-bottom: 40rpx;
}

/* 商品卡片 */
.product-card {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.product-card:active {
  transform: scale(0.98);
}

/* 商品图片 */
.product-image-container {
  position: relative;
  width: 100%;
  height: 300rpx;
}

.product-image {
  width: 100%;
  height: 100%;
}

.seckill-badge {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-weight: 500;
}

/* 商品信息 */
.product-info {
  padding: 20rpx;
}

.product-name {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
  height: 72rpx;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-bottom: 16rpx;
}

/* 价格区域 */
.price-section {
  display: flex;
  align-items: baseline;
  margin-bottom: 16rpx;
}

.current-price {
  font-size: 32rpx;
  color: #ff6b6b;
  font-weight: 600;
  margin-right: 12rpx;
}

.original-price {
  font-size: 22rpx;
  color: #999;
  text-decoration: line-through;
}

/* 库存进度条 */
.stock-progress {
  margin-bottom: 20rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff6b6b, #ff8e8e);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.stock-text {
  font-size: 22rpx;
  color: #666;
}

/* 操作区域 */
.action-section {
  display: flex;
  justify-content: center;
}

.buy-btn {
  width: 100%;
  height: 64rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: #fff;
  border: none;
  border-radius: 32rpx;
  font-size: 26rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.buy-btn:active {
  opacity: 0.8;
}

.sold-out-btn {
  width: 100%;
  height: 64rpx;
  background: #ccc;
  color: #fff;
  border: none;
  border-radius: 32rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 加载更多 */
.load-more {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;
}

.load-more-text {
  font-size: 26rpx;
  color: #999;
  padding: 20rpx 40rpx;
  background: #fff;
  border-radius: 40rpx;
  border: 1rpx solid #eee;
}
