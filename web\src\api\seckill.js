import axios from 'axios'
import api from '@/config/api'

// 设置基础URL
const baseURL = api.rootUrl

// 创建axios实例
const request = axios.create({
  baseURL: baseURL,
  timeout: 10000
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 可以在这里添加token等认证信息
    const token = localStorage.getItem('admin_token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    return response
  },
  error => {
    console.error('API请求错误:', error)
    return Promise.reject(error)
  }
)

// ==================== 秒杀时间段管理 ====================

/**
 * 获取秒杀时间段配置
 */
export function getSeckillTimeSlots() {
  return request({
    url: 'marketing/seckill/time_list',
    method: 'get'
  })
}

/**
 * 获取秒杀时间段和当前活跃时段
 */
export function getSeckillIndexTime() {
  return request({
    url: 'api/seckill/index',
    method: 'get'
  })
}

// ==================== 秒杀商品管理 ====================

/**
 * 获取秒杀商品列表
 * @param {Object} params - 查询参数
 */
export function getSeckillList(params) {
  return request({
    url: 'marketing/seckill',
    method: 'get',
    params
  })
}

/**
 * 获取指定时间段的秒杀商品列表
 * @param {Number} timeId - 时间段ID
 * @param {Object} params - 查询参数
 */
export function getSeckillListByTime(timeId, params) {
  return request({
    url: `api/seckill/list/${timeId}`,
    method: 'get',
    params
  })
}

/**
 * 获取秒杀商品详情
 * @param {Number} id - 商品ID
 */
export function getSeckillDetail(id) {
  return request({
    url: `marketing/seckill/${id}`,
    method: 'get'
  })
}

/**
 * 获取前端秒杀商品详情
 * @param {Number} id - 商品ID
 * @param {Object} params - 查询参数
 */
export function getSeckillProductDetail(id, params) {
  return request({
    url: `api/seckill/detail/${id}`,
    method: 'get',
    params
  })
}

// ==================== 秒杀活动管理 ====================

/**
 * 获取秒杀活动列表
 * @param {Object} params - 查询参数
 */
export function getSeckillActivityList(params) {
  return request({
    url: 'marketing/seckill_activity/list',
    method: 'get',
    params
  })
}

/**
 * 获取秒杀活动详情
 * @param {Number} id - 活动ID
 */
export function getSeckillActivityInfo(id) {
  return request({
    url: `marketing/seckill_activity/info/${id}`,
    method: 'get'
  })
}

/**
 * 创建秒杀商品
 * @param {Object} data - 商品数据
 */
export function createSeckill(data) {
  return request({
    url: `marketing/seckill/${data.id || 0}`,
    method: 'post',
    data
  })
}

/**
 * 更新秒杀商品状态
 * @param {Number} id - 商品ID
 * @param {Number} status - 状态
 */
export function updateSeckillStatus(id, status) {
  return request({
    url: `marketing/seckill/set_status/${id}/${status}`,
    method: 'put'
  })
}

/**
 * 删除秒杀商品
 * @param {Number} id - 商品ID
 */
export function deleteSeckill(id) {
  return request({
    url: `marketing/seckill/${id}`,
    method: 'delete'
  })
}

// ==================== 秒杀统计数据 ====================

/**
 * 获取秒杀统计数据
 */
export function getSeckillStatistics() {
  return request({
    url: 'marketing/seckill/statistics',
    method: 'get'
  })
}

/**
 * 获取秒杀订单统计
 * @param {Number} id - 商品ID
 * @param {Object} params - 查询参数
 */
export function getSeckillOrderStats(id, params) {
  return request({
    url: `marketing/seckill/order/${id}`,
    method: 'get',
    params
  })
}

// ==================== 首页秒杀数据 ====================

/**
 * 获取首页秒杀数据
 * @param {Object} params - 查询参数
 */
export function getHomeSeckillData(params) {
  return request({
    url: 'api/home/<USER>',
    method: 'get',
    params
  })
}

/**
 * 获取实时秒杀轮次数据
 */
export function getSeckillRounds() {
  return request({
    url: 'api/seckill/rounds',
    method: 'get'
  })
}

// ==================== 秒杀配置管理 ====================

/**
 * 获取秒杀系统配置
 */
export function getSeckillConfig() {
  return request({
    url: 'marketing/seckill/config',
    method: 'get'
  })
}

/**
 * 更新秒杀系统配置
 * @param {Object} data - 配置数据
 */
export function updateSeckillConfig(data) {
  return request({
    url: 'marketing/seckill/config',
    method: 'post',
    data
  })
}

export default {
  getSeckillTimeSlots,
  getSeckillIndexTime,
  getSeckillList,
  getSeckillListByTime,
  getSeckillDetail,
  getSeckillProductDetail,
  getSeckillActivityList,
  getSeckillActivityInfo,
  createSeckill,
  updateSeckillStatus,
  deleteSeckill,
  getSeckillStatistics,
  getSeckillOrderStats,
  getHomeSeckillData,
  getSeckillRounds,
  getSeckillConfig,
  updateSeckillConfig
}
