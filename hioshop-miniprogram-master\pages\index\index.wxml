<view class="container" wx:if="{{loading == 1}}">
    <view class='contact-wrap' wx:if="{{showContact == 1}}">
        <button class="contact-btn" session-from='{"nickName":"{{userInfo.nickname}}","avatarUrl":"{{userInfo.avatar}}"}' open-type="contact">
            <image class='icon' src='/images/icon/contact.png'></image>
            <view class='text'>客服</view>
        </button>
    </view>
    <view class="search-bar">
        <view class="search-container" bindtap="goSearch">
            <image class="search-icon" src="/images/icon/search.png"></image>
            <text class="search-placeholder">搜索,发现更多好物</text>
            <view class="scan-icon" catchtap="scanCode">
                <text class="scan-text">📷</text>
            </view>
        </view>
    </view>


    <view class='banner-wrap' wx:if="{{show_banner && banner.length > 0}}">
        <swiper class="banner" indicator-dots="true" autoplay="{{autoplay}}" interval="3000" duration="1000"   >
            <swiper-item wx:for="{{banner}}" wx:key="id">
                <navigator wx:if="{{item.link_type == 0}}" url="/pages/goods/goods?id={{item.goods_id}}">
                    <image src="{{item.image_url}}" background-size="cover"></image>
                </navigator>
                <navigator wx:elif="{{item.link_type == 1}}" url="{{item.link}}">
                    <image src="{{item.image_url}}" background-size="cover"></image>
                </navigator>
            </swiper-item>
        </swiper>
    </view>

    <!-- <ad unit-id="adunit-c755904541658aa1" ad-type="grid" grid-opacity="0.8" grid-count="5" ad-theme="white"></ad> -->
    <view class="catalog-wrap" wx:if="{{show_channel}}">
        <view class="category-grid">
            <view wx:for="{{channel}}" wx:if="{{item.sort_order < 9}}" wx:key="id" class='icon-navi' data-cateid="{{item.id}}" bindtap="goCategory">
                <image class='icon-img' src="{{item.icon_url}}"></image>
                <view class='icon-text'>{{item.name}}</view>
            </view>
        </view>
    </view>

    <!-- 签到区域 - CRMEB周签到模式 -->
    <view class="sign-in-zone">
        <sign-in bind:sign="onSignEvent"></sign-in>
    </view>



    <!-- CRMEB简洁卡片模式优惠券区域 -->
    <view class="crmeb-coupon-zone" wx:if="{{crmebCouponList.length > 0}}">
        <crmeb-coupon coupon-list="{{crmebCouponList}}" bind:receiveCoupon="onReceiveCoupon" bind:goCouponCenter="onGoCouponCenter"></crmeb-coupon>
    </view>

    <!-- 限时秒杀区域 -->
    <view class="seckill-zone" wx:if="{{seckillProducts.length > 0}}">
        <!-- 秒杀容器 - 统一容器包含头部和商品列表 -->
        <view class="seckill-container">
            <!-- 秒杀头部 - CRMEB风格 -->
            <view class="seckill-header" style="{{headerStyle}}">
                <view class="seckill-left">
                    <text class="seckill-title" style="{{titleStyle}}" wx:if="{{titleConfig}}">{{titleTxtConfig}}</text>
                    <image src="{{titleImg}}" class="seckill-title-image" wx:else></image>
                    <text class="countdown-tips" style="{{tipsColor}}">距离结束</text>
                    <count-down
                        is-day="{{false}}"
                        tip-text=" "
                        day-text=" "
                        hour-text=":"
                        minute-text=":"
                        second-text=" "
                        datatime="{{datatime}}"
                        bg-color="{{numberBgColor}}"
                        colors="{{numberColor}}">
                    </count-down>
                </view>
                <view class="seckill-right" style="{{headerBntColor}}" bindtap="goSeckillMore">
                    <text>{{rightBntTxt}}</text>
                    <text class="iconfont icon-ic_rightarrow"></text>
                </view>
            </view>

            <!-- 秒杀商品列表 -->
            <view class="seckill-products">
            <view class="seckill-product-item" wx:for="{{seckillProducts}}" wx:key="id" bindtap="goSeckillDetail" data-id="{{item.id}}">
                <!-- 商品图片 -->
                <view class="product-image-container">
                    <image class="product-image" src="{{item.image}}" mode="aspectFill"></image>
                </view>

                <!-- 商品信息 -->
                <view class="product-info">
                    <!-- 商品标题 -->
                    <view class="product-title">{{item.title}}</view>

                    <!-- 进度条 - 与CRMEB保持水平排列 -->
                    <view class="progress-section">
                        <view class="progress-bar">
                            <view class="progress-fill" style="width: {{item.progress}}%"></view>
                        </view>
                        <text class="progress-text">已抢{{item.progress}}%</text>
                    </view>

                    <!-- 价格和按钮 -->
                    <view class="price-action-row">
                        <view class="price-section">
                            <view class="price-row">
                                <text class="price-label">秒杀价</text>
                                <text class="current-price">¥ {{item.currentPrice}}</text>
                            </view>
                            <text class="original-price Regular">¥ {{item.originalPrice}}</text>
                        </view>
                        <button class="seckill-btn">去抢购</button>
                    </view>
                </view>
            </view>
        </view>
        </view>
    </view>

    <!-- 积分兑好礼区域 - CRMEB风格 -->
    <view class="points-mall-zone" wx:if="{{pointsGoodsList.length > 0}}">
        <points-mall points-goods-list="{{pointsGoodsList}}"></points-mall>
    </view>

    <!-- 新品推荐区域 -->
    <view class="new-products-section">
        <view class="section-header">
            <view class="section-title">新品推荐</view>
            <view class="section-more" bindtap="goMoreNewProducts">
                <text>更多</text>
                <image class="arrow-icon" src="/images/icon/arrow-right.png"></image>
            </view>
        </view>
        <view class="new-products-grid">
            <view class="product-card-new" wx:for="{{newProducts}}" wx:key="id" wx:if="{{index < 4}}" bindtap="goProductDetail" data-id="{{item.id}}">
                <view class="product-image-container">
                    <image class="product-image-new" src="{{item.list_pic_url}}" mode="aspectFill"></image>
                    <view class="new-badge-tag">新品</view>
                </view>
                <view class="product-info-new">
                    <view class="product-title-new">{{item.name}}</view>
                    <view class="product-price-row">
                        <view class="product-price-new">￥{{item.min_retail_price}}</view>
                        <view class="add-cart-btn-new" bindtap="addToCart" data-id="{{item.id}}" catchtap="addToCart">
                            <image class="cart-icon-new" src="/images/nav/icon-cart-b.png"></image>
                        </view>
                    </view>
                    <view class="product-meta">
                        <text class="sold-count">{{item.sold_count || 0}}人已购</text>
                        <view class="rating">
                            <image class="star-icon" src="/images/icon/star.png"></image>
                            <text class="rating-text">{{item.rating || '4.8'}}</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>

    <view class="goods-container">
        <view class='topic-container' wx:for="{{floorGoods}}" wx:key="id">
            <view class='banner-container' bindtap="goCategory" data-cateid="{{item.id}}" wx:if="{{index_banner_img == 1}}">
                <image mode='aspectFill' style="width:100%;height:{{item.height}}rpx" src='{{item.banner}}'>
                </image>
                <view class="bg" style="height:{{item.height}}rpx;line-height:{{item.height}}rpx;"></view>
                <view class="text" style="height:{{item.height}}rpx;line-height:{{item.height}}rpx;">{{item.name}}</view>
            </view>
            <view wx:else class="category-title" data-cateid="{{item.id}}" bindtap="goCategory">
                <view class="title">
                    <view class="text">{{item.name}}</view>
                    <view class="line"></view>
                </view>
            </view>
            <view class='category-products-grid'>
                <view class="product-card-category" wx:for="{{item.goodsList}}" wx:for-index="iindex" wx:for-item="iitem" wx:key="id" wx:if="{{iindex < 6}}">
                    <navigator hover-class='none' class='navi-url' url="/pages/goods/goods?id={{iitem.id}}">
                        <view class="product-image-container">
                            <image src="{{iitem.list_pic_url}}" class="product-image-category" mode="aspectFill">
                                <view wx:if="{{iitem.is_new == 1}}" class='new-badge-tag'>新品</view>
                            </image>
                            <block wx:if="{{iitem.goods_number <= 0}}">
                                <view class='sold-overlay'>
                                    <image class='soldout-icon' src='/images/icon/sold-out.png'></image>
                                </view>
                            </block>
                        </view>
                        <view class="product-info-category {{iitem.goods_number <= 0?'fast-out-status':''}}">
                            <view class="product-title-category">{{iitem.name}}</view>
                            <view class='product-price-row'>
                                <view class='product-price-category'>￥{{iitem.min_retail_price}}</view>
                                <view class="add-cart-btn-category" bindtap="addToCart" data-id="{{iitem.id}}" catchtap="addToCart">
                                    <image class="cart-icon-category" src="/images/nav/icon-cart-b.png"></image>
                                </view>
                            </view>
                            <view class="product-meta">
                                <text class="sold-count">{{iitem.sold_count || 0}}人已购</text>
                                <view class="rating">
                                    <image class="star-icon" src="/images/icon/star.png"></image>
                                    <text class="rating-text">{{iitem.rating || '4.8'}}</text>
                                </view>
                            </view>
                        </view>
                    </navigator>
                </view>
            </view>
        </view>
    </view>
    <!-- <ad unit-id="adunit-b6fcdb43b7913591"></ad> -->
    <view class="no-more-goods ">没有更多商品啦</view>
</view>
<loading show="{{loading != 1}}" text="美汐缘加载中..."></loading>
<mini-loading show="{{showMiniLoading}}" text="{{miniLoadingText}}"></mini-loading>