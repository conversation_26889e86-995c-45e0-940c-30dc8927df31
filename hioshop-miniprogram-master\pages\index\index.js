const util = require('../../utils/util.js');
const api = require('../../config/api.js');
const user = require('../../services/user.js');
const auth = require('../../utils/auth.js');

//获取应用实例
const app = getApp()

Page({
    data: {
        floorGoods: [],
        openAttr: false,
        showChannel: 0,
        showBanner: 0,
        showBannerImg: 0,
        banner: [],
        index_banner_img: 0,
        userInfo: {},
        imgurl: '',
        sysHeight: 0,
        loading: 0,
        autoplay: true,
        showContact: 0,  // 设置为0隐藏客服悬浮窗
        newProducts: [], // 新品推荐数据
        showMiniLoading: false, // 小型加载动画显示状态
        miniLoadingText: '', // 小型加载动画文本
        seckillProducts: [], // 秒杀商品数据
        seckillTimeLeft: {
            hours: 21,
            minutes: 30,
            seconds: 12
        }, // 秒杀倒计时
        seckillTimer: null, // 秒杀倒计时定时器

        // CRMEB风格秒杀配置
        datatime: Math.floor(Date.now() / 1000) + 3600 * 21 + 60 * 30 + 12, // 结束时间戳
        titleConfig: false, // 是否使用文字标题（改为false使用图片）
        titleTxtConfig: '限时秒杀', // 标题文字
        titleImg: '/images/seckill01.png', // 标题图片（使用crmeb的图片）
        rightBntTxt: '更多', // 右侧按钮文字

        // 样式配置 - 按照CRMEB默认配置
        headerStyle: 'background: linear-gradient(90deg, #fff 0%, #fff 100%);',
        titleStyle: 'color: #333333; font-size: 32rpx; font-weight: 500; font-family: PingFang SC;',
        tipsColor: 'color: #999999; font-family: PingFang SC;',
        headerBntColor: 'color: #999999; font-size: 24rpx; font-family: PingFang SC;',
        numberBgColor: 'linear-gradient(90deg, #000000 0%, #333333 100%)',
        numberColor: '#ffffff',

        // 积分兑好礼数据 - CRMEB风格
        pointsGoodsList: [],

        // CRMEB简洁卡片模式优惠券数据
        crmebCouponList: []
    },
    onLoad: function (options) {
        this.getChannelShowInfo();
        // 获取秒杀商品数据
        this.getSeckillProducts();
        // 获取积分商品数据
        this.getPointsGoods();
        // 获取秒杀时间配置
        this.getSeckillIndexTime();
        // 获取CRMEB优惠券数据
        this.getCrmebCouponList();
    },
    onPageScroll: function (e) {
        let scrollTop = e.scrollTop;
        let that = this;
        if (scrollTop >= 2000) {
            that.setData({
                showContact: 0
            })
        } else {
            that.setData({
                showContact: 1
            })
        }
    },
    onHide: function () {
        this.setData({
            autoplay: false
        })
    },
    goSearch: function () {
        wx.navigateTo({
            url: '/pages/search/search',
        })
    },

    // 扫码功能
    scanCode: function () {
        console.log('扫码方法被调用');

        wx.scanCode({
            // onlyFromCamera: true, // 开发时注释掉，真机部署时可以开启
            success: (res) => {
                console.log('扫码成功:', res);
                this.handleScanResult(res.result);
            },
            fail: (err) => {
                console.error('扫码失败:', err);
                if (err.errMsg.includes('cancel')) {
                    console.log('用户取消扫码');
                } else {
                    wx.showToast({
                        title: '扫码失败',
                        icon: 'none'
                    });
                }
            }
        });
    },

    // 处理扫码结果
    handleScanResult: function (result) {
        console.log('扫码结果:', result);

        // 检查是否是登录二维码
        if (result.startsWith('hioshop://login?token=')) {
            // 提取token
            const token = result.split('token=')[1];
            this.confirmQrLogin(token);
        } else {
            // 其他类型的二维码，显示结果
            wx.showModal({
                title: '扫码结果',
                content: `扫码内容: ${result}`,
                showCancel: false,
                success: function() {
                    console.log('扫码结果已显示');
                }
            });
        }
    },

    // 确认二维码登录
    confirmQrLogin: function (token) {
        const that = this;

        // 检查用户是否已登录
        const userInfo = wx.getStorageSync('userInfo');
        const userToken = wx.getStorageSync('token');

        console.log('检查登录状态:');
        console.log('userInfo:', userInfo);
        console.log('token:', userToken);

        if (!userInfo || !userToken) {
            wx.showModal({
                title: '提示',
                content: '请先登录小程序',
                showCancel: false,
                success: function () {
                    wx.navigateTo({
                        url: '/pages/app-auth/index'
                    });
                }
            });
            return;
        }

        wx.showModal({
            title: '确认登录',
            content: '确认使用当前账号登录管理后台？',
            success: function (res) {
                if (res.confirm) {
                    that.processQrLogin(token);
                }
            }
        });
    },

    // 处理二维码登录
    processQrLogin: function (token) {
        const that = this;

        this.setData({
            showMiniLoading: true,
            miniLoadingText: '登录中...'
        });

        // 调用API确认登录
        util.request(api.ConfirmQrLogin, {
            qrToken: token
        }, 'POST').then(function (res) {
            that.setData({
                showMiniLoading: false
            });

            if (res.errno === 0) {
                wx.showToast({
                    title: '登录成功',
                    icon: 'success'
                });
            } else {
                wx.showToast({
                    title: res.errmsg || '登录失败',
                    icon: 'none'
                });
            }
        }).catch(function (err) {
            that.setData({
                showMiniLoading: false
            });
            console.error('登录失败:', err);
            wx.showToast({
                title: '网络错误',
                icon: 'none'
            });
        });
    },



    goCategory: function (e) {
        let id = e.currentTarget.dataset.cateid;
        wx.setStorageSync('categoryId', id);
        wx.switchTab({
            url: '/pages/category/index',
        })
    },
    handleTap: function (event) {
        //阻止冒泡 
    },
    onShareAppMessage: function () {
        let info = wx.getStorageSync('userInfo');
        return {
            title: '美汐缘',
            desc: '微信小程序商城',
            path: '/pages/index/index?id=' + info.id
        }
    },
    toDetailsTap: function () {
        wx.navigateTo({
            url: '/pages/goods-details/index',
        });
    },
    getIndexData: function () {
        let that = this;
        util.request(api.IndexUrl).then(function (res) {
            if (res.errno === 0) {
                that.setData({
                    floorGoods: res.data.categoryList,
                    banner: res.data.banner,
                    channel: res.data.channel,
                    notice: res.data.notice,
                    loading: 1,
                });
                // 获取新品推荐数据
                that.getNewProducts();
                // 获取CRMEB优惠券数据
                that.getCrmebCouponList();
                let cartGoodsCount = '';
                if (res.data.cartCount == 0) {
                    wx.removeTabBarBadge({
                        index: 2,
                    })
                } else {
                    cartGoodsCount = res.data.cartCount + '';
                    wx.setTabBarBadge({
                        index: 2,
                        text: cartGoodsCount
                    })
                }
            }
        });
    },

    onShow: function () {
        this.getIndexData();
        var that = this;
        let userInfo = wx.getStorageSync('userInfo');
        if (userInfo != '') {
            that.setData({
                userInfo: userInfo,
            });
        };
        let info = wx.getSystemInfoSync();
        let sysHeight = info.windowHeight - 100;
        this.setData({
            sysHeight: sysHeight,
            autoplay: true
        });
        wx.removeStorageSync('categoryId');
    },
    getChannelShowInfo: function (e) {
        let that = this;
        util.request(api.ShowSettings).then(function (res) {
            if (res.errno === 0) {
                let show_channel = res.data.channel;
                let show_banner = res.data.banner;
                let show_notice = res.data.notice;
                let index_banner_img = res.data.index_banner_img;
                that.setData({
                    show_channel: show_channel,
                    show_banner: show_banner,
                    show_notice: show_notice,
                    index_banner_img: index_banner_img
                });
            }
        });
    },
    onPullDownRefresh: function () {
        wx.showNavigationBarLoading()
        this.getIndexData();
        this.getChannelShowInfo();
        wx.hideNavigationBarLoading() //完成停止加载
        wx.stopPullDownRefresh() //停止下拉刷新
    },

    // 获取新品推荐数据
    getNewProducts: function() {
        let that = this;
        util.request(api.GoodsList, {
            is_new: 1,  // 只获取新品
            page: 1,
            size: 4     // 只要4个商品
        }).then(function (res) {
            if (res.errno === 0) {
                that.setData({
                    newProducts: res.data
                });
            }
        });
    },

    // 跳转到商品详情
    goProductDetail: function(e) {
        let id = e.currentTarget.dataset.id;
        wx.navigateTo({
            url: '/pages/goods/goods?id=' + id
        });
    },

    // 查看更多新品
    goMoreNewProducts: function() {
        wx.navigateTo({
            url: '/pages/category/index?is_new=1'
        });
    },

    // 添加到购物车
    addToCart: function(e) {
        let goodsId = e.currentTarget.dataset.id;
        let that = this;

        // 检查用户是否登录
        let userInfo = wx.getStorageSync('userInfo');
        if (!userInfo) {
            wx.navigateTo({
                url: '/pages/app-auth/index'
            });
            return;
        }

        // 先获取商品的默认规格信息
        util.request(api.GoodsDetail, {
            id: goodsId
        }).then(function (goodsRes) {
            if (goodsRes.errno === 0) {
                let productList = goodsRes.data.productList;
                if (productList && productList.length > 0) {
                    // 使用第一个规格作为默认规格
                    let defaultProduct = productList[0];

                    // 添加到购物车
                    util.request(api.CartAdd, {
                        addType: 0,
                        goodsId: goodsId,
                        number: 1,
                        productId: defaultProduct.id
                    }, 'POST').then(function (res) {
                        if (res.errno === 0) {
                            wx.showToast({
                                title: '已添加到购物车',
                                icon: 'success',
                                duration: 1500
                            });

                            // 更新购物车数量
                            that.updateCartCount();

                            // 延迟跳转到购物车页面
                            setTimeout(function() {
                                wx.switchTab({
                                    url: '/pages/cart/cart'
                                });
                            }, 1500);
                        } else {
                            wx.showToast({
                                title: res.errmsg || '添加失败',
                                icon: 'none',
                                duration: 2000
                            });
                        }
                    });
                } else {
                    wx.showToast({
                        title: '商品规格信息获取失败',
                        icon: 'none',
                        duration: 2000
                    });
                }
            } else {
                wx.showToast({
                    title: '商品信息获取失败',
                    icon: 'none',
                    duration: 2000
                });
            }
        });
    },

    // 更新购物车数量
    updateCartCount: function() {
        util.request(api.CartGoodsCount).then(function (res) {
            if (res.errno === 0) {
                let cartGoodsCount = res.data;
                if (cartGoodsCount == 0) {
                    wx.removeTabBarBadge({
                        index: 2,
                    });
                } else {
                    wx.setTabBarBadge({
                        index: 2,
                        text: cartGoodsCount + ''
                    });
                }
            }
        });
    },





    // 加入购物车方法
    addToCart: function(e) {
        const goodsId = e.currentTarget.dataset.id;

        if (!goodsId) {
            wx.showToast({
                title: '商品信息错误',
                icon: 'none'
            });
            return;
        }

        // 检查用户登录状态
        if (!app.globalData.hasLogin) {
            wx.showToast({
                title: '请先登录',
                icon: 'none'
            });
            setTimeout(() => {
                wx.navigateTo({
                    url: '/pages/auth/login/login'
                });
            }, 1500);
            return;
        }

        // 显示加载提示
        this.setData({
            showMiniLoading: true,
            miniLoadingText: '加入购物车中...'
        });

        // 调用加入购物车接口
        util.request(api.CartAdd, {
            goodsId: goodsId,
            number: 1,
            productId: 0
        }, 'POST').then(res => {
            that.setData({
                showMiniLoading: false
            });

            if (res.errno === 0) {
                wx.showToast({
                    title: '已加入购物车',
                    icon: 'success',
                    duration: 1500
                });

                // 更新购物车数量
                this.getCartCount();
            } else {
                wx.showToast({
                    title: res.errmsg || '加入购物车失败',
                    icon: 'none'
                });
            }
        }).catch(error => {
            that.setData({
                showMiniLoading: false
            });
            console.error('加入购物车失败:', error);
            wx.showToast({
                title: '网络错误，请重试',
                icon: 'none'
            });
        });
    },

    // 获取购物车数量
    getCartCount: function() {
        if (!app.globalData.hasLogin) {
            return;
        }

        util.request(api.CartGoodsCount).then(res => {
            if (res.errno === 0) {
                // 更新全局购物车数量
                app.globalData.cartGoodsCount = res.data;
            }
        });
    },

    // 获取秒杀商品数据
    getSeckillProducts: function() {
        let that = this;
        // 模拟秒杀商品数据，实际项目中应该从API获取
        const seckillProducts = [
            {
                id: 1,
                image: '/images/icon/default_goods.jpg',
                title: '真力时（ZENITH）瑞士手表DEFY系列CLASSIC经典款男士腕表',
                brand: 'ZENITH',
                progress: 100,
                currentPrice: 999.00,
                originalPrice: 61000.00
            },
            {
                id: 2,
                image: '/images/icon/default_goods.jpg',
                title: '阿迪达斯官网 adidas BBALL CAP COT 男女训练运动帽子FQ5270',
                brand: 'adidas',
                progress: 100,
                currentPrice: 9.00,
                originalPrice: 180.00
            },
            {
                id: 3,
                image: '/images/icon/default_goods.jpg',
                title: 'FOMIX 蛋壳椅 进口头层牛皮椅色单人沙发椅Egg chair设计师蛋椅',
                brand: 'FOMIX',
                progress: 65,
                currentPrice: 999.00,
                originalPrice: 7580.00
            }
        ];

        that.setData({
            seckillProducts: seckillProducts
        });
    },

    // 启动秒杀倒计时
    startSeckillTimer: function() {
        let that = this;

        if (that.data.seckillTimer) {
            clearInterval(that.data.seckillTimer);
        }

        // 更新datatime为当前时间加上剩余时间
        const currentTime = Math.floor(Date.now() / 1000);
        const totalSeconds = that.data.seckillTimeLeft.hours * 3600 +
                           that.data.seckillTimeLeft.minutes * 60 +
                           that.data.seckillTimeLeft.seconds;

        that.setData({
            datatime: currentTime + totalSeconds
        });

        const timer = setInterval(() => {
            let timeLeft = that.data.seckillTimeLeft;
            let { hours, minutes, seconds } = timeLeft;

            if (seconds > 0) {
                seconds--;
            } else if (minutes > 0) {
                minutes--;
                seconds = 59;
            } else if (hours > 0) {
                hours--;
                minutes = 59;
                seconds = 59;
            } else {
                // 倒计时结束
                clearInterval(timer);
                return;
            }

            that.setData({
                seckillTimeLeft: { hours, minutes, seconds }
            });
        }, 1000);

        that.setData({
            seckillTimer: timer
        });
    },

    // 页面卸载时清除定时器
    onUnload: function() {
        if (this.data.seckillTimer) {
            clearInterval(this.data.seckillTimer);
        }
    },

    // 跳转到秒杀商品详情
    goSeckillDetail: function(e) {
        let id = e.currentTarget.dataset.id;
        wx.navigateTo({
            url: '/pages/goods/goods?id=' + id
        });
    },

    // 跳转到秒杀列表页面
    goSeckillMore: function() {
        wx.navigateTo({
            url: '/pages/category/index?seckill=1'
        });
    },

    // 获取秒杀时间配置 - 模拟CRMEB的getSeckillIndexTime方法
    getSeckillIndexTime: function() {
        let that = this;
        // 模拟秒杀时间数据，实际项目中应该从API获取
        // 设置一个未来的结束时间戳（当前时间 + 21小时30分12秒）
        let endTime = Math.floor(Date.now() / 1000) + (21 * 3600) + (30 * 60) + 12;

        that.setData({
            datatime: endTime
        });

        // 启动倒计时
        that.startSeckillTimer();
    },

    // 跳转到秒杀列表页面
    goSeckillMore: function() {
        wx.navigateTo({
            url: '/pages/category/index?seckill=1'
        });
    },

    // 签到事件处理
    onSignEvent: function(e) {
        console.log('签到事件:', e.detail);
        // 这里可以添加签到逻辑
        // 例如：调用签到API，更新签到状态等
    },

    // 获取积分商品数据 - CRMEB风格
    getPointsGoods: function() {
        let that = this;

        // 模拟积分商品数据，实际项目中应该从API获取
        const mockPointsGoods = [
            {
                id: 1,
                title: '精美保温水杯',
                image: 'https://picsum.photos/224/224?random=1',
                price: 500,
                sales: 128
            },
            {
                id: 2,
                title: '无线蓝牙耳机',
                image: 'https://picsum.photos/224/224?random=2',
                price: 1200,
                sales: 89
            },
            {
                id: 3,
                title: '多功能手机支架',
                image: 'https://picsum.photos/224/224?random=3',
                price: 300,
                sales: 256
            },
            {
                id: 4,
                title: '快充无线充电器',
                image: 'https://picsum.photos/224/224?random=4',
                price: 800,
                sales: 167
            },
            {
                id: 5,
                title: '大容量充电宝',
                image: 'https://picsum.photos/224/224?random=5',
                price: 600,
                sales: 203
            },
            {
                id: 6,
                title: '智能手环',
                image: 'https://picsum.photos/224/224?random=6',
                price: 900,
                sales: 145
            }
        ];

        that.setData({
            pointsGoodsList: mockPointsGoods
        });
    },

    // 获取CRMEB优惠券数据 - 从后端API读取
    getCrmebCouponList: function() {
        let that = this;

        // 从后端API获取优惠券数据，与代金券使用相同的接口
        util.request(api.CouponAvailable).then(function (res) {
            console.log('CRMEB优惠券API响应:', res);

            if (res.errno === 0) {
                // 确保数据完整性
                let coupons = res.data || [];
                console.log('原始CRMEB优惠券数据:', coupons);

                // 将后端数据转换为CRMEB组件需要的格式
                coupons = coupons.map(function(coupon) {
                    // 根据领取状态设置is_use字段
                    let isUse = false; // 默认可领取
                    if (!coupon.canReceive) {
                        isUse = true; // 已领取或不可领取
                    }

                    return {
                        id: coupon.id,
                        type_name: coupon.name || '优惠券', // 券类型名称
                        coupon_price: coupon.discount_value || 0, // 优惠金额
                        use_min_price: coupon.min_amount || 0, // 最低使用金额
                        is_use: isUse, // 使用状态：false=可领取, true=已领取, 2=已过期
                        start_time: coupon.start_time || '',
                        end_time: coupon.end_time || '',
                        canReceive: coupon.canReceive !== false,
                        reason: coupon.reason || ''
                    };
                });

                console.log('处理后的CRMEB优惠券数据:', coupons);
                console.log('CRMEB优惠券数量:', coupons.length);

                // 详细打印每个优惠券的信息
                coupons.forEach(function(coupon, index) {
                    console.log(`CRMEB优惠券${index + 1}:`, {
                        id: coupon.id,
                        type_name: coupon.type_name,
                        coupon_price: coupon.coupon_price,
                        use_min_price: coupon.use_min_price,
                        is_use: coupon.is_use
                    });
                });

                that.setData({
                    crmebCouponList: coupons
                });
            } else {
                console.log('CRMEB优惠券API返回错误:', res.errmsg);
                that.setData({
                    crmebCouponList: []
                });
            }
        }).catch(function(error) {
            console.log('获取CRMEB优惠券失败:', error);
            that.setData({
                crmebCouponList: []
            });
        });
    },

    // CRMEB优惠券领取事件处理
    onReceiveCoupon: function(e) {
        const coupon = e.detail.coupon;
        console.log('领取CRMEB优惠券:', coupon);

        // 如果已领取或已过期，不执行操作
        if (coupon.is_use === true || coupon.is_use === 2) {
            wx.showToast({
                title: coupon.reason || '该优惠券不可领取',
                icon: 'none'
            });
            return;
        }

        let that = this;

        // 调用后端API进行实际的优惠券领取操作
        util.request(api.CouponReceive, {
            couponId: coupon.id
        }, 'POST').then(function(res) {
            console.log('CRMEB优惠券领取API响应:', res);

            if (res.errno === 0) {
                // 领取成功，更新券状态
                let crmebCouponList = that.data.crmebCouponList;
                for (let i = 0; i < crmebCouponList.length; i++) {
                    if (crmebCouponList[i].id === coupon.id) {
                        crmebCouponList[i].is_use = true;
                        crmebCouponList[i].canReceive = false;
                        crmebCouponList[i].reason = '已领取';
                        break;
                    }
                }

                that.setData({
                    crmebCouponList: crmebCouponList
                });

                wx.showToast({
                    title: '领取成功',
                    icon: 'success',
                    duration: 2000
                });
            } else {
                wx.showToast({
                    title: res.errmsg || '领取失败',
                    icon: 'none'
                });
            }
        }).catch(function(error) {
            console.log('CRMEB优惠券领取失败:', error);
            wx.showToast({
                title: '网络错误，请重试',
                icon: 'none'
            });
        });
    },

    // CRMEB优惠券中心跳转事件处理
    onGoCouponCenter: function() {
        console.log('跳转到优惠券中心');
        // 这里可以添加埋点统计等逻辑
    }
});