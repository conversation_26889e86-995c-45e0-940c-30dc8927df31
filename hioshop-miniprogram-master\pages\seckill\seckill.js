const { seckillApi } = require('../../utils/api.js');

Page({
  data: {
    seckillTime: [], // 秒杀时间段列表
    seckillTimeIndex: 0, // 当前选中的时间段索引
    seckillProducts: [], // 秒杀商品列表
    loading: false, // 加载状态
    page: 1, // 当前页码
    hasMore: true, // 是否还有更多数据
    
    // CRMEB风格配置
    headerStyle: 'background: linear-gradient(90deg, #fff 0%, #fff 100%);',
    titleStyle: 'color: #333333; font-size: 32rpx; font-weight: 500;'
  },

  onLoad: function (options) {
    console.log('秒杀页面加载参数:', options);
    
    // 获取秒杀时间段配置
    this.getSeckillTimeConfig();
  },

  onShow: function () {
    // 页面显示时刷新数据
    this.refreshData();
  },

  /**
   * 获取秒杀时间段配置
   */
  getSeckillTimeConfig: function() {
    const that = this;
    
    seckillApi.getSeckillIndexTime().then(res => {
      console.log('获取秒杀时间段配置成功:', res);
      
      if (res && res.seckillTime) {
        that.setData({
          seckillTime: res.seckillTime,
          seckillTimeIndex: res.seckillTimeIndex >= 0 ? res.seckillTimeIndex : 0
        });
        
        // 获取当前时间段的商品列表
        that.getSeckillProductsByTime();
      }
    }).catch(error => {
      console.error('获取秒杀时间段配置失败:', error);
      wx.showToast({
        title: '获取配置失败',
        icon: 'none'
      });
    });
  },

  /**
   * 获取指定时间段的秒杀商品列表
   */
  getSeckillProductsByTime: function() {
    const that = this;
    const { seckillTime, seckillTimeIndex, page } = that.data;
    
    if (!seckillTime || seckillTime.length === 0) {
      return;
    }
    
    const currentTimeSlot = seckillTime[seckillTimeIndex];
    if (!currentTimeSlot) {
      return;
    }
    
    that.setData({ loading: true });
    
    seckillApi.getSeckillListByTime(currentTimeSlot.id, {
      page: page,
      limit: 10
    }).then(res => {
      console.log('获取秒杀商品列表成功:', res);
      
      if (res && Array.isArray(res)) {
        const newProducts = res.map(item => ({
          id: item.id,
          goods_id: item.goods_id,
          name: item.name,
          image: item.image || '/images/icon/default_goods.jpg',
          price: item.price,
          ot_price: item.ot_price,
          stock: item.stock,
          quota: item.quota,
          quota_show: item.quota_show,
          percent: item.percent,
          sales: item.sales
        }));
        
        if (page === 1) {
          // 第一页，直接替换
          that.setData({
            seckillProducts: newProducts,
            hasMore: newProducts.length >= 10
          });
        } else {
          // 后续页，追加数据
          that.setData({
            seckillProducts: that.data.seckillProducts.concat(newProducts),
            hasMore: newProducts.length >= 10
          });
        }
      } else {
        that.setData({
          seckillProducts: page === 1 ? [] : that.data.seckillProducts,
          hasMore: false
        });
      }
    }).catch(error => {
      console.error('获取秒杀商品列表失败:', error);
      wx.showToast({
        title: '获取商品失败',
        icon: 'none'
      });
    }).finally(() => {
      that.setData({ loading: false });
    });
  },

  /**
   * 切换时间段
   */
  onTimeSlotChange: function(e) {
    const index = e.currentTarget.dataset.index;
    
    this.setData({
      seckillTimeIndex: index,
      page: 1,
      seckillProducts: [],
      hasMore: true
    });
    
    // 获取新时间段的商品列表
    this.getSeckillProductsByTime();
  },

  /**
   * 跳转到秒杀商品详情
   */
  goSeckillDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    const goodsId = e.currentTarget.dataset.goodsId || id;
    const timeId = this.data.seckillTimeIndex + 1;
    
    wx.navigateTo({
      url: `/pages/goods/goods?id=${goodsId}&seckillId=${id}&timeId=${timeId}&seckill=1`
    });
  },

  /**
   * 刷新数据
   */
  refreshData: function() {
    this.setData({
      page: 1,
      seckillProducts: [],
      hasMore: true
    });
    
    this.getSeckillProductsByTime();
  },

  /**
   * 加载更多数据
   */
  loadMore: function() {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }
    
    this.setData({
      page: this.data.page + 1
    });
    
    this.getSeckillProductsByTime();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    this.refreshData();
    
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 上拉加载更多
   */
  onReachBottom: function() {
    this.loadMore();
  },

  /**
   * 分享功能
   */
  onShareAppMessage: function() {
    return {
      title: '限时秒杀，超值好货等你抢！',
      path: '/pages/seckill/seckill'
    };
  }
});
