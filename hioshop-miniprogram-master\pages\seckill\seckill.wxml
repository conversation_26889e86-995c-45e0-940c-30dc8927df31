<!--pages/seckill/seckill.wxml-->
<view class="seckill-page">
  <!-- 页面头部 -->
  <view class="page-header" style="{{headerStyle}}">
    <view class="header-title" style="{{titleStyle}}">限时秒杀</view>
  </view>

  <!-- 时间段选择器 -->
  <view class="time-selector" wx:if="{{seckillTime.length > 0}}">
    <scroll-view class="time-scroll" scroll-x="true" scroll-with-animation="true">
      <view class="time-list">
        <view 
          class="time-item {{index === seckillTimeIndex ? 'active' : ''}}"
          wx:for="{{seckillTime}}" 
          wx:key="id"
          bindtap="onTimeSlotChange"
          data-index="{{index}}"
        >
          <view class="time-text">{{item.time}}</view>
          <view class="time-status {{item.status === 1 ? 'active' : item.status === 0 ? 'ended' : 'upcoming'}}">
            {{item.state}}
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 当前时间段倒计时 -->
  <view class="countdown-section" wx:if="{{seckillTime[seckillTimeIndex]}}">
    <view class="countdown-container">
      <text class="countdown-label">距离结束</text>
      <count-down
        is-day="{{false}}"
        tip-text=" "
        day-text=" "
        hour-text=":"
        minute-text=":"
        second-text=""
        datatime="{{seckillTime[seckillTimeIndex].stop}}"
        bg-color="linear-gradient(90deg, #000000 0%, #333333 100%)"
        colors="#ffffff">
      </count-down>
    </view>
  </view>

  <!-- 商品列表 -->
  <view class="products-container">
    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading && seckillProducts.length === 0}}">
      <view class="loading-text">正在加载...</view>
    </view>

    <!-- 空状态 -->
    <view class="empty-container" wx:if="{{!loading && seckillProducts.length === 0}}">
      <image class="empty-image" src="/images/icon/empty.png" mode="aspectFit"></image>
      <view class="empty-text">暂无秒杀商品</view>
    </view>

    <!-- 商品列表 -->
    <view class="products-grid" wx:if="{{seckillProducts.length > 0}}">
      <view 
        class="product-card"
        wx:for="{{seckillProducts}}" 
        wx:key="id"
        bindtap="goSeckillDetail"
        data-id="{{item.id}}"
        data-goods-id="{{item.goods_id}}"
      >
        <!-- 商品图片 -->
        <view class="product-image-container">
          <image class="product-image" src="{{item.image}}" mode="aspectFill"></image>
          <view class="seckill-badge">秒杀</view>
        </view>

        <!-- 商品信息 -->
        <view class="product-info">
          <view class="product-name">{{item.name}}</view>
          
          <!-- 价格信息 -->
          <view class="price-section">
            <view class="current-price">¥{{item.price}}</view>
            <view class="original-price">¥{{item.ot_price}}</view>
          </view>

          <!-- 库存进度条 -->
          <view class="stock-progress">
            <view class="progress-bar">
              <view class="progress-fill" style="width: {{item.percent}}%"></view>
            </view>
            <view class="stock-text">仅剩{{item.stock}}件</view>
          </view>

          <!-- 抢购按钮 -->
          <view class="action-section">
            <button class="buy-btn" wx:if="{{item.stock > 0}}">立即抢购</button>
            <button class="sold-out-btn" wx:else disabled>已抢完</button>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{seckillProducts.length > 0}}">
      <view class="load-more-text" wx:if="{{loading}}">正在加载更多...</view>
      <view class="load-more-text" wx:elif="{{!hasMore}}">没有更多商品了</view>
      <view class="load-more-text" wx:else bindtap="loadMore">点击加载更多</view>
    </view>
  </view>
</view>
