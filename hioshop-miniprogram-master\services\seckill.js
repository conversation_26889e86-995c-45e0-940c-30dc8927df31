/**
 * 秒杀相关API服务
 */

const util = require('../utils/util.js');
const api = require('../config/api.js');

/**
 * 获取秒杀时间段配置和当前活跃时段
 */
function getSeckillIndexTime() {
  return util.request(api.SeckillIndexTime, {}, 'GET');
}

/**
 * 获取指定时间段的秒杀商品列表
 * @param {Number} timeId - 时间段ID
 * @param {Object} params - 查询参数
 */
function getSeckillListByTime(timeId, params = {}) {
  return util.request(api.SeckillListByTime.replace('{timeId}', timeId), params, 'GET');
}

/**
 * 获取秒杀商品详情
 * @param {Number} id - 商品ID
 * @param {Object} params - 查询参数
 */
function getSeckillDetail(id, params = {}) {
  return util.request(api.SeckillDetail.replace('{id}', id), params, 'GET');
}

/**
 * 获取首页秒杀数据
 * @param {Object} params - 查询参数
 */
function getHomeSeckillData(params = {}) {
  return util.request(api.HomeSeckill, params, 'GET');
}

/**
 * 获取实时秒杀轮次数据
 */
function getSeckillRounds() {
  return util.request(api.SeckillRounds, {}, 'GET');
}

/**
 * 获取秒杀商品列表（通用）
 * @param {Object} params - 查询参数
 */
function getSeckillList(params = {}) {
  return util.request(api.SeckillList, params, 'GET');
}

module.exports = {
  getSeckillIndexTime,
  getSeckillListByTime,
  getSeckillDetail,
  getHomeSeckillData,
  getSeckillRounds,
  getSeckillList
};
