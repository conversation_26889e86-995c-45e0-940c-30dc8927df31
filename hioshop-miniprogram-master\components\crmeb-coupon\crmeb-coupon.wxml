<!-- CRMEB简洁卡片模式优惠券组件 -->
<view class="crmeb-coupon-wrap">
  <!-- 头部区域 - 容器外独立区域 -->
  <view class="header">
    <view class="title-section">
      <text class="title-text">优惠券</text>
    </view>
    <view class="more-section" bindtap="goCouponCenter">
      <text class="more-text">更多</text>
      <text class="arrow-icon">›</text>
    </view>
  </view>

  <!-- 优惠券列表容器 - 深橙色渐变背景 -->
  <view class="crmeb-coupon">
    <view class="coupon-wrapper">
      <scroll-view class="scroll-view" scroll-x="true" show-scrollbar="{{false}}">
        <view class="coupon-list">
          <view class="coupon-item" wx:for="{{couponList}}" wx:key="id" bindtap="receiveCoupon" data-item="{{item}}">
            <view class="coupon-name">{{item.type_name}}</view>
            <view class="coupon-content">
              <view class="money">
                <text class="currency">¥</text>
                <text class="number">{{item.coupon_price}}</text>
              </view>
              <view class="info">
                <text wx:if="{{item.use_min_price > 0}}">满{{item.use_min_price}}可用</text>
                <text wx:else>无门槛券</text>
              </view>
            </view>
            <view class="button" wx:if="{{item.is_use == false}}">去领取</view>
            <view class="button disabled" wx:elif="{{item.is_use == true}}">已领取</view>
            <view class="button expired" wx:else>已过期</view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</view>
