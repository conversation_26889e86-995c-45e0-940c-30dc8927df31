// CRMEB简洁卡片模式优惠券组件
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    couponList: {
      type: Array,
      value: []
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 领取优惠券
    receiveCoupon: function(e) {
      const item = e.currentTarget.dataset.item;

      // 触发父组件事件，由父组件处理领取逻辑和提示
      this.triggerEvent('receiveCoupon', {
        coupon: item
      });
    },

    // 跳转到优惠券中心
    goCouponCenter: function() {
      this.triggerEvent('goCouponCenter');
      
      wx.navigateTo({
        url: '/pages/ucenter/coupons/index'
      });
    }
  }
});
