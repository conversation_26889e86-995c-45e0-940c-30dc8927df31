# 小程序秒杀系统对接方案

## 🎯 方案概述

本方案实现了小程序前端与后端服务的秒杀系统完整对接，让您的小程序能够自动显示后端配置的秒杀活动和商品。

## 🏗️ 系统架构

```
小程序前端 ←→ 后端API接口 ←→ 秒杀服务层 ←→ 数据库
```

## 📁 文件结构

### 后端文件
- `service/src/api/controller/seckill.js` - 秒杀API控制器
- 数据库表：`hiolabs_flash_sale_config`, `hiolabs_flash_sale_rounds`, `hiolabs_flash_sale_goods`

### 前端文件
- `utils/api.js` - API配置文件
- `pages/index/index.js` - 首页秒杀功能
- `pages/seckill/seckill.*` - 秒杀列表页面

## 🔧 配置步骤

### 1. 后端配置

#### 1.1 确保数据库表存在
确保以下数据表已创建：
- `hiolabs_flash_sale_config` - 秒杀系统配置
- `hiolabs_flash_sale_rounds` - 秒杀轮次
- `hiolabs_flash_sale_goods` - 秒杀商品
- `hiolabs_goods` - 商品主表

#### 1.2 配置API路由
在 `service/src/config/route.js` 中添加秒杀路由：
```javascript
// 秒杀相关路由
[/^api\/seckill\/index$/, 'api/seckill/index', 'get'],
[/^api\/seckill\/list\/(\d+)$/, 'api/seckill/list?timeId=:1', 'get'],
[/^api\/seckill\/detail\/(\d+)$/, 'api/seckill/detail?id=:1', 'get'],
[/^api\/home\/seckill$/, 'api/seckill/homeSeckill', 'get'],
[/^api\/seckill\/rounds$/, 'api/seckill/rounds', 'get'],
```

### 2. 前端配置

#### 2.1 更新API基础URL
在 `utils/api.js` 中确认API_BASE_URL配置正确：
```javascript
const API_BASE_URL = 'http://127.0.0.1:8360'; // 根据实际情况修改
```

#### 2.2 页面配置
秒杀页面已自动添加到 `app.json` 的pages配置中。

## 📊 API接口说明

### 1. 获取秒杀时间段配置
- **接口**: `GET /api/seckill/index`
- **返回**: 时间段列表和当前活跃时段索引

### 2. 获取秒杀商品列表
- **接口**: `GET /api/seckill/list/{timeId}`
- **参数**: timeId - 时间段ID
- **返回**: 该时间段的秒杀商品列表

### 3. 获取秒杀商品详情
- **接口**: `GET /api/seckill/detail/{id}`
- **参数**: id - 商品ID, time_id - 时间段ID
- **返回**: 秒杀商品详细信息

### 4. 获取首页秒杀数据
- **接口**: `GET /api/home/<USER>
- **返回**: 首页展示的秒杀商品（限4个）

### 5. 获取秒杀轮次数据
- **接口**: `GET /api/seckill/rounds`
- **返回**: 今日所有秒杀轮次信息

## 🎨 前端功能特性

### 首页秒杀区域
- ✅ 实时倒计时显示
- ✅ CRMEB风格界面设计
- ✅ 商品价格对比展示
- ✅ 点击跳转到商品详情

### 秒杀列表页面
- ✅ 时间段切换功能
- ✅ 商品网格布局
- ✅ 库存进度条显示
- ✅ 下拉刷新和上拉加载
- ✅ 空状态和加载状态

## 🔄 数据流程

### 页面加载流程
1. 小程序启动 → 调用 `getSeckillIndexTime()` 获取时间段配置
2. 获取配置成功 → 调用 `getHomeSeckillData()` 获取首页秒杀商品
3. 设置倒计时 → 启动定时器更新倒计时显示

### 用户交互流程
1. 用户点击"更多" → 跳转到秒杀列表页面
2. 用户切换时间段 → 重新加载该时间段商品
3. 用户点击商品 → 跳转到商品详情页面

## 🛠️ 开发调试

### 1. 启动后端服务
```bash
cd service
npm start
```

### 2. 配置小程序开发工具
- 确保API_BASE_URL指向正确的后端地址
- 在开发工具中开启"不校验合法域名"选项

### 3. 测试接口
可以直接在浏览器中访问API接口测试：
- http://127.0.0.1:8360/api/seckill/index
- http://127.0.0.1:8360/api/home/<USER>

## 📝 数据库示例

### 秒杀配置表示例
```sql
INSERT INTO hiolabs_flash_sale_config (id, is_enabled, header_banner) 
VALUES (1, 1, '/images/seckill_banner.jpg');
```

### 秒杀轮次表示例
```sql
INSERT INTO hiolabs_flash_sale_rounds (time_slot_id, start_time, end_time, status) 
VALUES (1, UNIX_TIMESTAMP('2024-01-01 09:00:00'), UNIX_TIMESTAMP('2024-01-01 11:00:00'), 'active');
```

## 🚀 部署说明

### 生产环境配置
1. 修改 `utils/api.js` 中的API_BASE_URL为生产环境地址
2. 在微信公众平台配置服务器域名
3. 确保HTTPS证书配置正确

### 性能优化建议
1. 启用API接口缓存
2. 图片使用CDN加速
3. 合理设置定时器间隔
4. 实现接口防抖处理

## 🔍 故障排查

### 常见问题
1. **API请求失败**: 检查网络连接和API地址配置
2. **数据不显示**: 检查数据库中是否有有效的秒杀数据
3. **倒计时不准确**: 检查服务器时间和客户端时间同步
4. **页面跳转失败**: 检查页面路径配置是否正确

### 调试方法
1. 开启控制台日志查看API请求响应
2. 使用微信开发工具的网络面板检查请求
3. 在后端添加日志输出调试数据流

## 📞 技术支持

如有问题，请检查：
1. 后端服务是否正常运行
2. 数据库连接是否正常
3. API接口是否返回正确数据格式
4. 小程序页面配置是否正确

---

**注意**: 本方案基于您现有的系统架构设计，请根据实际情况调整配置参数。
