<!-- 签到组件 - CRMEB周签到模式 -->
<view class="sign-wrap-container">
  <view class="sign-in-container">
    <!-- 周签到模式 -->
    <view class="week-wrap">
      <view class="week-list">
        <view class="week-item" wx:for="{{signList}}" wx:key="index">
          <!-- 统一显示sign-icon-01.png -->
          <image src="/images/sign-icon-01.png" class="sign-icon" mode="widthFix"></image>
          <view class="week-text">{{item.weekText}}</view>
        </view>
      </view>
      <view class="sign-button" bindtap="onSignTap">签到</view>
    </view>
  </view>
</view>
