<!-- 积分兑好礼组件 - CRMEB风格 -->
<view class="points-mall-wrap">
  <view class="points-mall">
    <!-- 头部区域 -->
    <view class="header">
      <view class="title-section">
        <image src="/images/points02.png" class="title-image" mode="widthFix"></image>
      </view>
      <view class="more-section" bindtap="goPointsMall">
        <text class="more-text">更多</text>
        <text class="arrow-icon">›</text>
      </view>
    </view>

    <!-- 商品列表 - 横向滚动样式 -->
    <view class="goods-wrapper">
      <scroll-view class="scroll-view" scroll-x="true" show-scrollbar="{{false}}">
        <view class="item" wx:for="{{pointsGoodsList}}" wx:key="id" bindtap="goGoodsDetails" data-id="{{item.id}}">
          <image class="goods-image" src="{{item.image}}" mode="aspectFill"></image>
          <view class="title">{{item.title}}</view>
          <view class="price-info">
            <text class="price-num">{{item.price}}</text>
            <text class="price-unit">积分</text>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</view>
