/* 积分兑好礼组件样式 - CRMEB风格 */
.points-mall-wrap {
  margin: 20rpx 20rpx;
}

.points-mall {
  border-radius: 16rpx;
  overflow: hidden;
}

/* 头部区域 - 图片模式，无背景颜色 */
.header {
  height: 96rpx;
  padding: 0 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  display: flex;
  align-items: center;
}

.title-image {
  width: 176rpx;
  height: 32rpx;
}

.more-section {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999999;
}

.more-text {
  margin-right: 4rpx;
}

.arrow-icon {
  font-size: 24rpx;
  font-weight: bold;
}

/* 商品列表区域 */
.goods-wrapper {
  padding: 0 0 32rpx 20rpx;
}

.scroll-view {
  box-sizing: border-box;
  white-space: nowrap;
  padding: 20rpx;
  border-radius: 16rpx 0 0 16rpx;
  background: #FFFFFF;
}

.scroll-view .item {
  display: inline-block;
  width: 224rpx;
  margin: 0 20rpx 0 0;
  vertical-align: top;
}

.scroll-view .item:last-child {
  margin: 0;
}

.goods-image {
  width: 224rpx;
  height: 224rpx;
  border-radius: 12rpx;
}

.title {
  margin-top: 12rpx;
  font-size: 24rpx;
  line-height: 34rpx;
  color: #282828;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.price-info {
  margin-top: 8rpx;
  text-align: center;
}

.price-num {
  font-size: 28rpx;
  font-weight: 600;
  color: #ff4757;
  font-family: SemiBold;
}

.price-unit {
  font-size: 22rpx;
  color: #ff4757;
  margin-left: 2rpx;
  font-family: Regular;
}


