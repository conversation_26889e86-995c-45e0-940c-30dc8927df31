/* CRMEB简洁卡片模式优惠券组件样式 */
.crmeb-coupon-wrap {
  margin: 20rpx 20rpx;
}

/* 头部区域 - 容器外独立区域，与容器贴合 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24rpx;
  margin-bottom: 12rpx;
  background: transparent;
}

.title-section {
  display: flex;
  align-items: center;
}

.title-text {
  font-size: 34rpx;
  font-weight: 600;
  color: #2C405A;
  font-family: PingFang SC;
}

.more-section {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #999999;
  font-family: PingFang SC;
}

/* 优惠券列表容器 - CRMEB标准渐变背景 */
.crmeb-coupon {
  background: linear-gradient(90deg, #F73730 0%, #F86429 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(247, 55, 48, 0.25);
}

.more-text {
  margin-right: 4rpx;
}

.arrow-icon {
  font-size: 24rpx;
  font-weight: bold;
}

/* 优惠券列表区域 */
.coupon-wrapper {
  padding: 0;
}

.scroll-view {
  box-sizing: border-box;
  white-space: nowrap;
}

.coupon-list {
  display: flex;
  gap: 12rpx;
}

/* CRMEB模式2简洁卡片样式 */
.coupon-item {
  flex-shrink: 0;
  width: 204rpx;
  height: 200rpx;
  padding-top: 24rpx;
  border-radius: 16rpx;
  background: #FFFFFF;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f5f5f5;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 券类型名称 */
.coupon-name {
  text-align: center;
  font-weight: 500;
  font-size: 24rpx;
  line-height: 34rpx;
  color: #2C405A;
  margin-bottom: 2rpx;
  font-family: SemiBold;
}

/* 券内容区域 */
.coupon-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--view-theme, #ff6b35);
}

/* 金额显示 */
.money {
  height: 48rpx;
  font-weight: 500;
  font-size: 28rpx;
  display: flex;
  align-items: baseline;
  justify-content: center;
  color: #E93323;
}

.currency {
  font-size: 28rpx;
  font-family: Regular;
}

.number {
  font-family: SemiBold;
  font-size: 44rpx;
  margin-left: 2rpx;
}

/* 使用条件 */
.info {
  font-size: 18rpx;
  line-height: 26rpx;
  margin-top: 6rpx;
  color: #E93323;
}

/* 领取按钮 */
.button {
  width: 136rpx;
  height: 48rpx;
  border-radius: 24rpx;
  margin: 4rpx auto 0;
  background: linear-gradient(90deg, #E93323 0%, #FF7931 100%);
  text-align: center;
  font-weight: 500;
  font-size: 24rpx;
  line-height: 48rpx;
  color: #FFFFFF;
}

.button.disabled {
  background: #f5f5f5;
  color: #999999;
}

.button.expired {
  background: #f5f5f5;
  color: #999999;
}

/* CSS变量定义 - CRMEB主题色 */
:root {
  --view-theme: #E93323;
  --view-gradient: #FF7931;
  --view-priceColor: #E93323;
  --bg-star1: #F73730;
  --bg-end1: #F86429;
}
