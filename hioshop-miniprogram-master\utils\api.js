/**
 * 小程序API配置文件
 * 用于统一管理API接口地址
 */

// 后端服务基础URL - 根据您的web/src/config/api.js配置
const API_BASE_URL = 'http://127.0.0.1:8360';

/**
 * 发起网络请求
 * @param {Object} options 请求配置
 */
function request(options) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: API_BASE_URL + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        'Authorization': wx.getStorageSync('token') || '',
        ...options.header
      },
      success: (res) => {
        console.log('API请求成功:', options.url, res.data);
        
        if (res.data.errno === 0) {
          resolve(res.data.data);
        } else {
          console.error('API业务错误:', res.data.errmsg);
          reject(new Error(res.data.errmsg || '请求失败'));
        }
      },
      fail: (error) => {
        console.error('API请求失败:', options.url, error);
        reject(error);
      }
    });
  });
}

/**
 * 秒杀相关API接口
 */
const seckillApi = {
  
  /**
   * 获取秒杀时间段配置和当前活跃时段
   */
  getSeckillIndexTime() {
    return request({
      url: '/api/seckill/index',
      method: 'GET'
    });
  },
  
  /**
   * 获取指定时间段的秒杀商品列表
   * @param {Number} timeId 时间段ID
   * @param {Object} params 查询参数
   */
  getSeckillListByTime(timeId, params = {}) {
    return request({
      url: `/api/seckill/list/${timeId}`,
      method: 'GET',
      data: params
    });
  },
  
  /**
   * 获取秒杀商品详情
   * @param {Number} id 商品ID
   * @param {Number} timeId 时间段ID
   */
  getSeckillDetail(id, timeId = 0) {
    return request({
      url: `/api/seckill/detail/${id}`,
      method: 'GET',
      data: { time_id: timeId }
    });
  },
  
  /**
   * 获取首页秒杀数据
   */
  getHomeSeckillData() {
    return request({
      url: '/api/home/<USER>',
      method: 'GET'
    });
  },
  
  /**
   * 获取实时秒杀轮次数据
   */
  getSeckillRounds() {
    return request({
      url: '/api/seckill/rounds',
      method: 'GET'
    });
  }
};

/**
 * 商品相关API接口
 */
const goodsApi = {
  
  /**
   * 获取商品列表
   * @param {Object} params 查询参数
   */
  getGoodsList(params = {}) {
    return request({
      url: '/api/goods/list',
      method: 'GET',
      data: params
    });
  },
  
  /**
   * 获取商品详情
   * @param {Number} id 商品ID
   */
  getGoodsDetail(id) {
    return request({
      url: `/api/goods/detail/${id}`,
      method: 'GET'
    });
  }
};

/**
 * 用户相关API接口
 */
const userApi = {
  
  /**
   * 用户登录
   * @param {Object} data 登录数据
   */
  login(data) {
    return request({
      url: '/api/auth/login',
      method: 'POST',
      data: data
    });
  },
  
  /**
   * 获取用户信息
   */
  getUserInfo() {
    return request({
      url: '/api/user/info',
      method: 'GET'
    });
  }
};

/**
 * 订单相关API接口
 */
const orderApi = {
  
  /**
   * 创建订单
   * @param {Object} data 订单数据
   */
  createOrder(data) {
    return request({
      url: '/api/order/create',
      method: 'POST',
      data: data
    });
  },
  
  /**
   * 获取订单列表
   * @param {Object} params 查询参数
   */
  getOrderList(params = {}) {
    return request({
      url: '/api/order/list',
      method: 'GET',
      data: params
    });
  },
  
  /**
   * 获取订单详情
   * @param {Number} id 订单ID
   */
  getOrderDetail(id) {
    return request({
      url: `/api/order/detail/${id}`,
      method: 'GET'
    });
  }
};

/**
 * 首页相关API接口
 */
const indexApi = {
  
  /**
   * 获取首页数据
   */
  getIndexData() {
    return request({
      url: '/api/index/data',
      method: 'GET'
    });
  },
  
  /**
   * 获取轮播图
   */
  getBannerList() {
    return request({
      url: '/api/index/banner',
      method: 'GET'
    });
  }
};

// 导出API接口
module.exports = {
  request,
  seckillApi,
  goodsApi,
  userApi,
  orderApi,
  indexApi,
  API_BASE_URL
};
