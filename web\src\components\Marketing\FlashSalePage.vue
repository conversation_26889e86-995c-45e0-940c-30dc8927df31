<template>
  <div class="flash-sale-page">
    <!-- 页面头部 -->
    <div class="page-header bg-white shadow-sm border-b">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">轮次秒杀</h1>
            <p class="text-sm text-gray-500 mt-1">每5分钟一轮，间隔2分钟，可选择不同商品参与秒杀</p>
          </div>
          <div class="flex items-center space-x-3">
            <button @click="showAddModal = true" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              <i class="ri-add-line mr-2"></i>
              创建轮次
            </button>
            <button @click="showConfigModal = true" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
              <i class="ri-settings-line mr-2"></i>
              系统配置
            </button>
            <button @click="refreshData" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <i class="ri-refresh-line mr-2"></i>
              刷新数据
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="p-6 max-w-7xl mx-auto">
      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <i class="ri-flashlight-line text-xl text-red-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">秒杀活动总数</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.totalFlashSales }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="ri-play-circle-line text-xl text-green-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">进行中</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.activeFlashSales }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <i class="ri-shopping-cart-line text-xl text-yellow-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">秒杀订单</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.flashSaleOrders }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <i class="ri-money-dollar-circle-line text-xl text-purple-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">秒杀销售额</p>
              <p class="text-2xl font-bold text-gray-900">¥{{ statistics.flashSaleSales }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 实时轮次状态 -->
      <div class="bg-white rounded-lg shadow-sm border mb-6">
        <div class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">实时秒杀轮次</h3>
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-500">自动刷新</span>
              <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            </div>
          </div>

          <!-- 当前进行中的轮次 -->
          <div v-if="currentRounds.length > 0" class="mb-6">
            <h4 class="text-md font-medium text-gray-800 mb-3 flex items-center">
              <span class="w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse"></span>
              正在进行 ({{ currentRounds.length }}场)
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div
                v-for="round in currentRounds"
                :key="round.id"
                class="p-4 border-2 border-red-200 bg-red-50 rounded-lg"
              >
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-red-700">第{{ round.round_number }}轮</span>
                  <span class="px-2 py-1 bg-red-500 text-white text-xs rounded-full">进行中</span>
                </div>
                <div class="text-lg font-semibold text-gray-900 mb-1">{{ round.campaign.name }}</div>
                <div class="text-sm text-gray-600 mb-2">
                  ¥{{ round.campaign.flash_price }}
                  <span class="line-through text-gray-400 ml-1">¥{{ round.campaign.original_price }}</span>
                </div>
                <div class="flex justify-between text-xs text-gray-500">
                  <span>库存: {{ round.stock - round.sold_count }}/{{ round.stock }}</span>
                  <span>{{ formatTime(round.end_time) }} 结束</span>
                </div>
                <div class="mt-2 bg-gray-200 rounded-full h-2">
                  <div
                    class="bg-red-500 h-2 rounded-full transition-all duration-300"
                    :style="{ width: (round.sold_count / round.stock * 100) + '%' }"
                  ></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 即将开始的轮次 -->
          <div v-if="upcomingRounds.length > 0">
            <h4 class="text-md font-medium text-gray-800 mb-3 flex items-center">
              <span class="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
              即将开始 ({{ upcomingRounds.length }}场)
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div
                v-for="round in upcomingRounds"
                :key="round.id"
                class="p-4 border border-orange-200 bg-orange-50 rounded-lg"
              >
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-orange-700">第{{ round.round_number }}轮</span>
                  <span class="px-2 py-1 bg-orange-500 text-white text-xs rounded-full">
                    {{ formatCountdown(round.countdown) }}
                  </span>
                </div>
                <div class="text-md font-semibold text-gray-900 mb-1">{{ round.campaign.name }}</div>
                <div class="text-sm text-gray-600 mb-2">
                  ¥{{ round.campaign.flash_price }}
                </div>
                <div class="text-xs text-gray-500">
                  {{ formatTime(round.start_time) }} 开始
                </div>
              </div>
            </div>
          </div>

          <!-- 无轮次时的提示 -->
          <div v-if="currentRounds.length === 0 && upcomingRounds.length === 0" class="text-center py-8">
            <i class="ri-time-line text-4xl text-gray-300 mb-2"></i>
            <p class="text-gray-500">暂无进行中或即将开始的秒杀轮次</p>
            <p class="text-sm text-gray-400 mt-1">请创建秒杀活动或等待下一轮开始</p>
          </div>
        </div>
      </div>

      <!-- 筛选和搜索 -->
      <div class="bg-white rounded-lg shadow-sm border mb-6">
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">搜索商品</label>
              <input
                v-model="searchQuery"
                type="text"
                placeholder="输入商品名称"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">活动状态</label>
              <select
                v-model="filterStatus"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">全部状态</option>
                <option value="upcoming">即将开始</option>
                <option value="active">进行中</option>
                <option value="ended">已结束</option>
                <option value="disabled">已停用</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">商品分类</label>
              <select
                v-model="filterCategory"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">全部分类</option>
                <option value="electronics">数码电器</option>
                <option value="clothing">服装鞋帽</option>
                <option value="home">家居用品</option>
                <option value="beauty">美妆护肤</option>
              </select>
            </div>

            <div class="flex items-end">
              <button @click="resetFilters" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                <i class="ri-refresh-line mr-2"></i>
                重置
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 秒杀商品列表 -->
      <div class="bg-white rounded-lg shadow-sm border">
        <div class="px-6 py-4 border-b">
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900">
              秒杀商品列表
              <span v-if="selectedTimeSlot" class="text-sm font-normal text-gray-500 ml-2">
                ({{ selectedTimeSlot.name }} {{ selectedTimeSlot.time }})
              </span>
            </h2>
            <div class="flex items-center space-x-2">
              <button
                @click="viewMode = 'grid'"
                :class="[
                  'px-3 py-1 text-sm rounded-md transition-colors',
                  viewMode === 'grid' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                ]"
              >
                <i class="ri-grid-line mr-1"></i>
                网格视图
              </button>
              <button
                @click="viewMode = 'list'"
                :class="[
                  'px-3 py-1 text-sm rounded-md transition-colors',
                  viewMode === 'list' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                ]"
              >
                <i class="ri-list-check mr-1"></i>
                列表视图
              </button>
            </div>
          </div>
        </div>

        <!-- 网格视图 -->
        <div v-if="viewMode === 'grid'" class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <div v-for="product in filteredProducts" :key="product.id" class="bg-white border rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
              <!-- 商品图片 -->
              <div class="relative">
                <img :src="product.image" :alt="product.name" class="w-full h-48 object-cover">
                <div class="absolute top-2 left-2">
                  <span :class="[
                    'px-2 py-1 text-xs rounded-full',
                    product.status === 'active' ? 'bg-red-100 text-red-800' :
                    product.status === 'upcoming' ? 'bg-yellow-100 text-yellow-800' :
                    product.status === 'ended' ? 'bg-gray-100 text-gray-800' :
                    'bg-red-100 text-red-800'
                  ]">
                    {{ getStatusLabel(product.status) }}
                  </span>
                </div>
                <div class="absolute top-2 right-2">
                  <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                    {{ product.category }}
                  </span>
                </div>
                <div v-if="product.status === 'active'" class="absolute bottom-2 left-2">
                  <div class="bg-red-600 text-white px-2 py-1 rounded text-xs">
                    <i class="ri-time-line mr-1"></i>
                    {{ product.remainingTime }}
                  </div>
                </div>
              </div>

              <!-- 商品信息 -->
              <div class="p-4">
                <h3 class="font-medium text-gray-900 mb-2 line-clamp-2">{{ product.name }}</h3>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between items-center">
                    <span class="text-gray-500">原价:</span>
                    <span class="text-gray-400 line-through">¥{{ product.originalPrice }}</span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-500">秒杀价:</span>
                    <span class="font-medium text-red-600 text-lg">¥{{ product.flashPrice }}</span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-500">库存:</span>
                    <span class="font-medium">{{ product.stock }}</span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-500">已售:</span>
                    <span class="font-medium text-green-600">{{ product.soldCount }}</span>
                  </div>
                </div>

                <!-- 进度条 -->
                <div class="mt-3">
                  <div class="flex justify-between text-xs text-gray-500 mb-1">
                    <span>销售进度</span>
                    <span>{{ Math.round((product.soldCount / (product.soldCount + product.stock)) * 100) }}%</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div
                      class="bg-red-600 h-2 rounded-full transition-all duration-300"
                      :style="{ width: Math.round((product.soldCount / (product.soldCount + product.stock)) * 100) + '%' }"
                    ></div>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="mt-4 space-y-2">
                  <div class="flex space-x-2">
                    <button @click="viewProduct(product)" class="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                      详情
                    </button>
                    <button @click="editProduct(product)" class="flex-1 px-3 py-2 bg-green-600 text-white text-sm rounded hover:bg-green-700">
                      编辑
                    </button>
                  </div>
                  <button @click="toggleProductStatus(product)" :class="[
                    'w-full px-3 py-2 text-sm rounded transition-colors',
                    product.status === 'active'
                      ? 'bg-red-600 text-white hover:bg-red-700'
                      : 'bg-green-600 text-white hover:bg-green-700'
                  ]">
                    {{ product.status === 'active' ? '停止秒杀' : '开始秒杀' }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 列表视图 -->
        <div v-else class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">商品信息</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">原价</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">秒杀价</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">库存</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">已售</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="product in filteredProducts" :key="product.id" class="hover:bg-gray-50">
                <!-- 商品信息 -->
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <img :src="product.image" :alt="product.name" class="w-12 h-12 rounded-lg object-cover">
                    <div class="ml-3">
                      <div class="text-sm font-medium text-gray-900 max-w-xs truncate">{{ product.name }}</div>
                      <div class="text-sm text-gray-500">ID: {{ product.id }}</div>
                    </div>
                  </div>
                </td>

                <!-- 分类 -->
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                    {{ product.category }}
                  </span>
                </td>

                <!-- 原价 -->
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400 line-through">
                  ¥{{ product.originalPrice }}
                </td>

                <!-- 秒杀价 -->
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">
                  ¥{{ product.flashPrice }}
                </td>

                <!-- 库存 -->
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ product.stock }}
                </td>

                <!-- 已售 -->
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                  {{ product.soldCount }}
                </td>

                <!-- 状态 -->
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="[
                    'px-2 py-1 text-xs rounded-full',
                    product.status === 'active' ? 'bg-red-100 text-red-800' :
                    product.status === 'upcoming' ? 'bg-yellow-100 text-yellow-800' :
                    product.status === 'ended' ? 'bg-gray-100 text-gray-800' :
                    'bg-red-100 text-red-800'
                  ]">
                    {{ getStatusLabel(product.status) }}
                  </span>
                </td>

                <!-- 操作 -->
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button @click="viewProduct(product)" class="text-blue-600 hover:text-blue-900">详情</button>
                  <button @click="editProduct(product)" class="text-green-600 hover:text-green-900">编辑</button>
                  <button @click="toggleProductStatus(product)" :class="[
                    'transition-colors',
                    product.status === 'active' ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'
                  ]">
                    {{ product.status === 'active' ? '停止' : '开始' }}
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <div class="px-6 py-4 border-t bg-gray-50">
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-500">
              显示 {{ Math.min(filteredProducts.length, 20) }} 条，共 {{ filteredProducts.length }} 条记录
            </div>
            <div class="flex items-center space-x-2">
              <button class="px-3 py-1 border rounded-md text-sm hover:bg-gray-100">
                上一页
              </button>
              <span class="px-3 py-1 text-sm">1 / 1</span>
              <button class="px-3 py-1 border rounded-md text-sm hover:bg-gray-100">
                下一页
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建秒杀弹窗 -->
    <div v-if="showAddModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-3xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">创建秒杀活动</h3>
          <button @click="showAddModal = false" class="text-gray-400 hover:text-gray-600">
            <i class="ri-close-line text-xl"></i>
          </button>
        </div>

        <div class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">选择商品</label>
              <select
                v-model="newFlashSale.productId"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">请选择商品</option>
                <option value="1">轻奢纯棉刺绣水洗四件套</option>
                <option value="2">秋冬保暖加厚澳洲羊毛被</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">选择时段</label>
              <select
                v-model="newFlashSale.timeSlotId"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">请选择时段</option>
                <option v-for="slot in timeSlots" :key="slot.id" :value="slot.id">
                  {{ slot.name }} {{ slot.time }}
                </option>
              </select>
            </div>
          </div>

          <div class="grid grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">秒杀价格</label>
              <input
                v-model="newFlashSale.flashPrice"
                type="number"
                step="0.01"
                placeholder="请输入秒杀价格"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">秒杀库存</label>
              <input
                v-model="newFlashSale.stock"
                type="number"
                placeholder="请输入秒杀库存"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">限购数量</label>
              <input
                v-model="newFlashSale.limitQuantity"
                type="number"
                placeholder="每人限购数量"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">开始时间</label>
              <input
                v-model="newFlashSale.startTime"
                type="datetime-local"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">结束时间</label>
              <input
                v-model="newFlashSale.endTime"
                type="datetime-local"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          <div class="flex justify-end space-x-3 pt-4 border-t">
            <button @click="showAddModal = false" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
              取消
            </button>
            <button @click="createFlashSale" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
              创建秒杀
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 时段管理弹窗 -->
    <div v-if="showTimeSlotModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">秒杀时段管理</h3>
          <button @click="showTimeSlotModal = false" class="text-gray-400 hover:text-gray-600">
            <i class="ri-close-line text-xl"></i>
          </button>
        </div>

        <div class="space-y-4">
          <div class="grid grid-cols-1 gap-4">
            <div v-for="slot in timeSlots" :key="slot.id" class="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <div class="font-medium text-gray-900">{{ slot.name }}</div>
                <div class="text-sm text-gray-500">{{ slot.time }}</div>
                <div class="text-xs text-gray-400">{{ slot.productCount }} 个商品</div>
              </div>
              <div class="flex items-center space-x-2">
                <button class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                  编辑
                </button>
                <button class="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700">
                  删除
                </button>
              </div>
            </div>
          </div>

          <div class="flex justify-end space-x-3 pt-4 border-t">
            <button @click="showTimeSlotModal = false" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
              关闭
            </button>
            <button class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
              添加时段
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getSeckillTimeSlots,
  getSeckillIndexTime,
  getSeckillList,
  getSeckillListByTime,
  getSeckillActivityList,
  getSeckillStatistics,
  getSeckillRounds,
  createSeckill,
  updateSeckillStatus
} from '@/api/seckill'

export default {
  name: 'FlashSalePage',
  data() {
    return {
      // 视图模式
      viewMode: 'grid',

      // 搜索和筛选
      searchQuery: '',
      filterStatus: '',
      filterCategory: '',

      // 弹窗状态
      showAddModal: false,
      showTimeSlotModal: false,
      showConfigModal: false,

      // 统计数据
      statistics: {
        totalFlashSales: 0,
        activeFlashSales: 0,
        flashSaleOrders: 0,
        flashSaleSales: '0.00'
      },

      // 秒杀时间段数据
      timeSlots: [],
      selectedTimeSlot: null,

      // 新秒杀活动数据
      newFlashSale: {
        productId: '',
        timeSlotId: '',
        flashPrice: '',
        stock: '',
        limitQuantity: 1,
        startTime: '',
        endTime: ''
      },

      // 当前轮次
      currentRounds: [],

      // 即将开始的轮次
      upcomingRounds: [],

      // 秒杀商品列表
      products: [],

      // 活动列表
      campaigns: [],

      // 加载状态
      loading: false,
      statisticsLoading: false,
      timeSlotsLoading: false,

      // 自动刷新定时器
      refreshTimer: null
    }
  },

  mounted() {
    this.loadData();
    // 设置定时刷新轮次数据
    this.refreshTimer = setInterval(() => {
      this.loadRounds();
      this.loadTimeSlots();
    }, 30000); // 每30秒刷新一次
  },

  beforeDestroy() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
  },

  computed: {
    filteredProducts() {
      let filtered = this.products;

      // 根据选中的时段筛选
      if (this.selectedTimeSlot) {
        filtered = filtered.filter(product =>
          product.time_id && product.time_id.includes(this.selectedTimeSlot.id.toString())
        );
      }

      if (this.searchQuery) {
        filtered = filtered.filter(product =>
          (product.title || product.name || '').toLowerCase().includes(this.searchQuery.toLowerCase())
        );
      }

      if (this.filterStatus) {
        filtered = filtered.filter(product => {
          // 根据CRMEB的状态映射
          const statusMap = {
            'active': 1,
            'upcoming': 2,
            'ended': 0,
            'disabled': -1
          };
          return product.status === statusMap[this.filterStatus];
        });
      }

      if (this.filterCategory) {
        filtered = filtered.filter(product => product.category === this.filterCategory);
      }

      return filtered;
    }
  },

  methods: {
    // 加载所有数据
    async loadData() {
      await Promise.all([
        this.loadStatistics(),
        this.loadRounds(),
        this.loadCampaigns()
      ]);
    },

    // 加载统计数据
    async loadStatistics() {
      this.statisticsLoading = true;
      try {
        const response = await this.axios.get('flashsale/statistics');
        if (response.data.errno === 0) {
          this.statistics = response.data.data;
        } else {
          console.error('获取统计数据失败:', response.data.errmsg);
        }
      } catch (error) {
        console.error('获取统计数据失败:', error);
        this.$message.error('获取统计数据失败');
      } finally {
        this.statisticsLoading = false;
      }
    },

    // 加载轮次数据
    async loadRounds() {
      try {
        const response = await this.axios.get('flashsale/rounds');
        if (response.data.errno === 0) {
          this.currentRounds = response.data.data.current || [];
          this.upcomingRounds = response.data.data.upcoming || [];

          // 为即将开始的轮次计算倒计时
          this.upcomingRounds.forEach(round => {
            const startTime = new Date(round.start_time);
            const now = new Date();
            round.countdown = Math.max(0, Math.floor((startTime - now) / 1000));
          });
        } else {
          console.error('获取轮次数据失败:', response.data.errmsg);
        }
      } catch (error) {
        console.error('获取轮次数据失败:', error);
      }
    },

    // 加载活动列表
    async loadCampaigns() {
      this.loading = true;
      try {
        const params = {
          search: this.searchQuery,
          status: this.filterStatus,
          page: 1,
          limit: 50
        };

        const response = await this.axios.get('flashsale/campaigns', { params });
        if (response.data.errno === 0) {
          this.campaigns = response.data.data;
        } else {
          console.error('获取活动列表失败:', response.data.errmsg);
        }
      } catch (error) {
        console.error('获取活动列表失败:', error);
        this.$message.error('获取活动列表失败');
      } finally {
        this.loading = false;
      }
    },

    // 获取状态标签
    getStatusLabel(status) {
      const labels = {
        active: '进行中',
        upcoming: '即将开始',
        ended: '已结束',
        disabled: '已停用'
      };
      return labels[status] || status;
    },

    // 选择时段
    selectTimeSlot(timeSlot) {
      this.selectedTimeSlot = this.selectedTimeSlot?.id === timeSlot.id ? null : timeSlot;
      this.loadProducts(); // 重新加载商品列表
    },

    // 重置筛选
    resetFilters() {
      this.searchQuery = '';
      this.filterStatus = '';
      this.filterCategory = '';
    },

    // 格式化时间显示
    formatTime(timeStr) {
      const date = new Date(timeStr);
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    // 格式化倒计时
    formatCountdown(seconds) {
      if (seconds <= 0) return '即将开始';

      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;

      if (minutes > 0) {
        return `${minutes}分${remainingSeconds}秒`;
      } else {
        return `${remainingSeconds}秒`;
      }
    },

    // 查看商品详情
    viewProduct(product) {
      console.log('查看商品详情:', product);
      this.$message.info('查看功能开发中');
    },

    // 编辑商品
    editProduct(product) {
      console.log('编辑商品:', product);
      this.$message.info('编辑功能开发中');
    },

    // 切换商品状态
    toggleProductStatus(product) {
      console.log('切换商品状态:', product);
      this.$message.info('状态切换功能开发中');
    },

    // 创建秒杀活动
    async createFlashSale() {
      try {
        const response = await this.axios.post('flashsale/create', this.newFlashSale);
        if (response.data.errno === 0) {
          this.showAddModal = false;
          this.$message.success('秒杀活动创建成功');
          this.loadData(); // 重新加载数据
          // 重置表单
          this.newFlashSale = {
            productId: '',
            timeSlotId: '',
            flashPrice: '',
            stock: '',
            limitQuantity: '',
            startTime: '',
            endTime: ''
          };
        } else {
          this.$message.error(response.data.errmsg || '创建失败');
        }
      } catch (error) {
        console.error('创建秒杀活动失败:', error);
        this.$message.error('创建失败');
      }
    },

    // 刷新数据
    refreshData() {
      this.loadData();
      this.$message.success('数据刷新成功');
    }
  }
}
</script>

<style scoped>
.flash-sale-page {
  min-height: 100vh;
  background-color: #f9fafb;
}

.page-header {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* 表格样式 */
.overflow-x-auto {
  overflow-x: auto;
}

table {
  min-width: 100%;
}

/* 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-1.md\\:grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-2.md\\:grid-cols-4.lg\\:grid-cols-6 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
</style>