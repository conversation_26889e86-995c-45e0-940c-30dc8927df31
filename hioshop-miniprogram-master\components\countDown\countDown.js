Component({
    properties: {
        // 距离开始提示文字
        tipText: {
            type: String,
            value: "倒计时"
        },
        dayText: {
            type: String,
            value: "天"
        },
        hourText: {
            type: String,
            value: "时"
        },
        minuteText: {
            type: String,
            value: "分"
        },
        secondText: {
            type: String,
            value: "秒"
        },
        // 结束时间戳
        datatime: {
            type: Number,
            value: 0
        },
        // 是否显示天数
        isDay: {
            type: Boolean,
            value: true
        },
        // 背景颜色
        bgColor: {
            type: String,
            value: ""
        },
        // 文字颜色
        colors: {
            type: String,
            value: ""
        },
        // 对齐方式
        justifyLeft: {
            type: String,
            value: ""
        }
    },

    data: {
        day: "00",
        hour: "00",
        minute: "00",
        second: "00",
        timer: null
    },

    lifetimes: {
        attached() {
            this.showTime();
        },
        detached() {
            if (this.data.timer) {
                clearInterval(this.data.timer);
            }
        }
    },

    methods: {
        showTime() {
            const that = this;

            function runTime() {
                // 时间函数
                let intDiff = that.properties.datatime - Date.parse(new Date()) / 1000; // 获取数据中的时间戳的时间差
                let day = 0,
                    hour = 0,
                    minute = 0,
                    second = 0;

                if (intDiff > 0) {
                    // 转换时间
                    if (that.properties.isDay === true) {
                        day = Math.floor(intDiff / (60 * 60 * 24));
                    } else {
                        day = 0;
                    }
                    hour = Math.floor(intDiff / (60 * 60)) - day * 24;
                    minute = Math.floor(intDiff / 60) - day * 24 * 60 - hour * 60;
                    second = Math.floor(intDiff) - day * 24 * 60 * 60 - hour * 60 * 60 - minute * 60;

                    if (hour <= 9) hour = "0" + hour;
                    if (minute <= 9) minute = "0" + minute;
                    if (second <= 9) second = "0" + second;

                    that.setData({
                        day: day,
                        hour: hour,
                        minute: minute,
                        second: second
                    });
                } else {
                    that.setData({
                        day: "00",
                        hour: "00",
                        minute: "00",
                        second: "00"
                    });
                    // 倒计时结束，清除定时器
                    if (that.data.timer) {
                        clearInterval(that.data.timer);
                    }
                }
            }

            runTime();
            const timer = setInterval(runTime, 1000);
            this.setData({ timer });
        }
    }
});
